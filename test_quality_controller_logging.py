#!/usr/bin/env python3
"""
测试质控主控制器日志修改是否正确的简单脚本
"""

import sys
import os
import logging

# 设置测试日志级别为DEBUG以查看所有日志
logging.basicConfig(level=logging.DEBUG)

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入修改后的模块
try:
    from dev_v1.Quality_Control.quality_controller import quality_control
    print("✓ 质控主控制器模块导入成功")
except ImportError as e:
    print(f"✗ 质控主控制器模块导入失败: {e}")
    sys.exit(1)

# 测试数据
test_data = {
    "Patient": {
        "PatientId": "TEST001",
        "PatientName": "测试患者",
        "Age": 45,
        "SexName": "男"
    },
    "RepSummaryInfo": {
        "DisHospitalDrugs": ""  # 空的出院医嘱，应该触发质控问题
    },
    "VisitInfo": {
        "AdmissionNumber": "Y001",
        "DeptName": "心内科"
    }
}

print("开始测试质控主控制器功能...")
print("=" * 50)

try:
    # 测试出院小结质控
    processed_data, quality_info = quality_control("DischargeSummary", test_data)
    
    print("✓ 质控主控制器执行成功")
    print(f"文档类型: {quality_info.get('document_type', 'Unknown')}")
    
    status = quality_info.get('status', {})
    print(f"最终得分: {status.get('final_score', 'Unknown')}/{status.get('base_score', 'Unknown')}")
    print(f"质控结果: {status.get('quality_status', 'Unknown')}")
    print(f"是否通过: {status.get('passed', 'Unknown')}")
    
    summary = quality_info.get('summary', {})
    print(f"发现问题总数: {summary.get('total_issues', 'Unknown')}")
    
    recommendations = summary.get('recommendations', [])
    if recommendations:
        print("改进建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    
except Exception as e:
    print(f"✗ 质控主控制器执行失败: {e}")
    import traceback
    traceback.print_exc()

print("=" * 50)
print("测试完成！")
print("注意：上面的日志输出应该使用 logging 模块而不是 print 语句")

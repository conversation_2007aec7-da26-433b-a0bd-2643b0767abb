"""
质控主控制器
Quality Control Main Controller

作为总的质控控制器，分别调用规则质控和内涵质控模块
支持出院小结的规则质控和内涵质控功能
"""

import os
import sys
from typing import Dict, Any, Union, List

# 添加项目路径以支持模块导入
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

import logging

# 导入内涵质控模块
try:
    from .Connotation_Quality_Control.DischargeSummary_QualityControl import quality_control as discharge_summary_connotation_qc
    print("成功导入出院小结内涵质控模块")
except ImportError as e:
    try:
        # 尝试绝对导入
        sys.path.append(os.path.join(current_dir, 'Connotation_Quality_Control'))
        from DischargeSummary_QualityControl import quality_control as discharge_summary_connotation_qc
        print("成功导入出院小结内涵质控模块（绝对路径）")
    except ImportError as e2:
        print(f"导入出院小结内涵质控模块失败: {str(e)} | {str(e2)}")
        discharge_summary_connotation_qc = None

# 导入规则质控模块（预留接口）
try:
    # from Regulatory_Quality_Control import some_regulatory_module
    print("规则质控模块导入预留")
except ImportError as e:
    print(f"规则质控模块导入失败: {str(e)}")


def discharge_summary_connotation_quality_control(data: dict) -> list:
    """
    出院小结内涵质控函数

    调用内涵质控模块对出院小结数据进行内涵质控检查，
    重点检查出院医嘱的完整性和合理性。

    Args:
        data: 出院小结数据对象，包含Patient、RepSummaryInfo、VisitInfo三个主要部分

    Returns:
        list: 质控结果列表，每个问题一个字典，格式为：
        [{
            "score": int,  # 扣分分数
            "message": str,  # 具体问题描述
            "suggestions": str  # 改进建议
        }]
    """
    try:
        print("开始执行出院小结内涵质控检查")

        # 检查内涵质控模块是否可用
        if discharge_summary_connotation_qc is None:
            print("错误：内涵质控模块未成功导入")
            return [{
                "score": 0,
                "message": "内涵质控模块不可用",
                "suggestions": "请检查内涵质控模块的导入配置"
            }]

        # 调用内涵质控模块
        quality_issues = discharge_summary_connotation_qc(data)

        if quality_issues['success']:
            num_issue = len(quality_issues["data"]["RepSummaryInfo"]["DisHospitalDrugs"])
            if num_issue == 0:
                print(f"内涵质控检查通过，未发现问题")
            else:
                print(f"内涵质控检查完成，发现 {num_issue} 个问题")
        else:
            print("内涵质控检查失败")

        # 记录质控结果详情
        if quality_issues:
            total_score = sum(issue.get('score', 0) for issue in quality_issues)
            print(f"内涵质控总扣分: {total_score}")
            for i, issue in enumerate(quality_issues, 1):
                print(f"  问题{i}: 扣分{issue.get('score', 0)} - {issue.get('message', '')[:50]}...")
        else:
            print("内涵质控检查通过，未发现问题")

        return quality_issues

    except Exception as e:
        print(f"出院小结内涵质控执行异常: {str(e)}")
        return [{
            "score": 0,
            "message": f"内涵质控执行异常: {str(e)}",
            "suggestions": "请检查数据格式和质控模块配置"
        }]


def discharge_summary_total_quality_control(data: dict) -> dict:
    """
    出院小结总质控函数

    整合所有出院小结相关的质控检查，包括规则质控和内涵质控，
    计算总扣分和最终得分，判断质控通过状态。

    Args:
        data: 出院小结数据对象，包含Patient、RepSummaryInfo、VisitInfo三个主要部分

    Returns:
        dict: 质控结果字典，格式为：
        {
            "data": {
                # 原始数据 + 质控结果
                "质控函数原位置": list  # 详细的质控问题列表
            },
            "status": {
                "total_deduction": int,  # 总扣分
                "final_score": int,      # 最终得分（120 - 总扣分）
                "passed": bool,          # 是否通过（final_score >= 60）
            }
        }
    """
    # 质控配置参数
    BASE_SCORE = 120  # 基准总分
    PASS_THRESHOLD = 60  # 及格线

    try:
        print("开始执行出院小结总质控检查")
        print(f"质控配置: 基准分{BASE_SCORE}分，及格线{PASS_THRESHOLD}分")

        # 初始化结果结构
        result_data = data.copy()  # 复制原始数据
        all_quality_issues = []
        total_deduction = 0

        # 1. 执行规则质控检查（预留接口）
        print("执行规则质控检查...")
        regulatory_issues = _execute_regulatory_quality_control(data)
        if regulatory_issues:
            all_quality_issues.extend(regulatory_issues)
            regulatory_deduction = sum(issue.get('score', 0) for issue in regulatory_issues)
            total_deduction += regulatory_deduction
            print(f"规则质控发现 {len(regulatory_issues)} 个问题，扣分 {regulatory_deduction}")
        else:
            print("规则质控检查通过，未发现问题")

        # 2. 执行内涵质控检查
        print("执行内涵质控检查...")
        connotation_issues = discharge_summary_connotation_quality_control(data)
        if connotation_issues:
            all_quality_issues.extend(connotation_issues)
            connotation_deduction = sum(issue.get('score', 0) for issue in connotation_issues)
            total_deduction += connotation_deduction
            print(f"内涵质控发现 {len(connotation_issues)} 个问题，扣分 {connotation_deduction}")
        else:
            print("内涵质控检查通过，未发现问题")

        # 3. 计算最终得分和通过状态
        final_score = max(0, BASE_SCORE - total_deduction)  # 确保分数不为负
        passed = final_score >= PASS_THRESHOLD

        # 4. 构建返回结果
        # 将质控结果精确存储到出现问题的具体字段位置
        result_data = _store_quality_issues_to_fields(data, all_quality_issues)

        result = {
            "data": result_data,
            "status": {
                "total_deduction": int(total_deduction),
                "final_score": int(final_score),
                "passed": bool(passed)
            }
        }

        # 5. 记录质控总结
        print("=" * 50)
        print("出院小结总质控检查完成")
        print(f"发现问题总数: {len(all_quality_issues)}")
        print(f"总扣分: {total_deduction}")
        print(f"最终得分: {final_score}/{BASE_SCORE}")
        print(f"质控结果: {'通过' if passed else '不通过'}")
        print("=" * 50)

        return result

    except Exception as e:
        print(f"出院小结总质控执行异常: {str(e)}")

        # 异常情况下的返回结果
        error_issues = [{
            "score": 0,
            "message": f"总质控执行异常: {str(e)}",
            "suggestions": "请检查数据格式和质控模块配置"
        }]

        # 将异常信息也存储到具体字段位置
        error_data = _store_quality_issues_to_fields(data, error_issues)

        error_result = {
            "data": error_data,
            "status": {
                "total_deduction": 0,
                "final_score": BASE_SCORE,
                "passed": "Error"  # 异常情况下默认错误，避免误判
            }
        }

        return error_result


def _store_quality_issues_to_fields(original_data: dict, quality_issues: List[dict]) -> dict:
    """
    将质控结果精确存储到出现问题的具体字段位置

    Args:
        original_data: 原始出院小结数据
        quality_issues: 所有质控问题列表

    Returns:
        dict: 修改后的数据，质控结果存储在具体字段位置
    """
    try:
        # 深拷贝原始数据，避免修改原始对象
        import copy
        result_data = copy.deepcopy(original_data)

        # 如果没有质控问题，直接返回原始数据
        if not quality_issues:
            print("无质控问题，返回原始数据")
            return result_data

        # 分析质控问题并分类存储
        field_issues = _categorize_quality_issues_by_field(quality_issues)

        # 将质控结果存储到对应字段
        for field_path, issues in field_issues.items():
            print(f"将 {len(issues)} 个质控问题存储到字段: {field_path}")
            _set_field_value(result_data, field_path, issues)

        return result_data

    except Exception as e:
        print(f"存储质控结果到字段时发生异常: {str(e)}")
        # 异常情况下返回原始数据
        return original_data


def _categorize_quality_issues_by_field(quality_issues: List[dict]) -> Dict[str, List[dict]]:
    """
    根据质控问题的内容分析其来源字段，并进行分类

    Args:
        quality_issues: 质控问题列表

    Returns:
        dict: 字段路径到问题列表的映射
    """
    field_issues = {}

    for issue in quality_issues:
        message = issue.get('message', '').lower()

        # 根据问题描述判断来源字段
        target_field = _identify_problem_field(message, issue)

        if target_field not in field_issues:
            field_issues[target_field] = []

        field_issues[target_field].append(issue)
        print(f"问题归类到字段 {target_field}: {issue.get('message', '')[:50]}...")

    return field_issues


def _identify_problem_field(message: str, issue: dict) -> str:
    """
    根据质控问题的描述识别问题来源字段

    Args:
        message: 问题描述（小写）
        issue: 完整的问题对象

    Returns:
        str: 字段路径，如 "RepSummaryInfo.DisHospitalDrugs"
    """
    # 出院医嘱相关问题 -> RepSummaryInfo.DisHospitalDrugs
    discharge_drug_keywords = [
        '出院医嘱', '出院带药', '药名', '剂量', '用法', '带药总量',
        '随访要求', '注意事项', 'dishospitaldrugs'
    ]

    if any(keyword in message for keyword in discharge_drug_keywords):
        return "RepSummaryInfo.DisHospitalDrugs"

    # 数据结构问题 -> 根级别
    structure_keywords = ['数据结构', '数据格式', 'repsummaryinfo', '缺少.*部分']
    if any(keyword in message for keyword in structure_keywords):
        return "RepSummaryInfo"

    # 患者信息问题 -> Patient
    patient_keywords = ['患者信息', '患者', 'patient']
    if any(keyword in message for keyword in patient_keywords):
        return "Patient"

    # 就诊信息问题 -> VisitInfo
    visit_keywords = ['就诊信息', '科室', '医师', 'visitinfo']
    if any(keyword in message for keyword in visit_keywords):
        return "VisitInfo"

    # 默认归类到出院医嘱字段（因为当前主要是内涵质控）
    return "RepSummaryInfo.DisHospitalDrugs"


def _set_field_value(data: dict, field_path: str, value: any) -> None:
    """
    根据字段路径设置数据值

    Args:
        data: 数据对象
        field_path: 字段路径，如 "RepSummaryInfo.DisHospitalDrugs"
        value: 要设置的值
    """
    try:
        path_parts = field_path.split('.')

        # 导航到目标字段的父级
        current = data
        for part in path_parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]

        # 设置最终字段的值
        final_field = path_parts[-1]
        current[final_field] = value

        print(f"成功设置字段 {field_path} 的值")

    except Exception as e:
        print(f"设置字段 {field_path} 时发生异常: {str(e)}")

def _execute_connotation_quality_control(data: dict) -> List[dict]:
    """
    执行内涵质控检查（预留接口）

    Args:
        data: 各种病历数据对象

    Returns:
        list: 规则质控问题列表
    """
    try:
        # TODO: 这里是规则质控的预留接口
        # 当规则质控模块完成后，在这里调用相应的规则质控函数
        return []

    except Exception as e:
        print(f"规则质控执行异常: {str(e)}")
        return [{
            "score": 0,
            "message": f"规则质控执行异常: {str(e)}",
            "suggestions": "请检查规则质控模块配置"
        }]

def _execute_regulatory_quality_control(data: dict) -> List[dict]:
    """
    执行规则质控检查（预留接口）

    Args:
        data: 各种病历数据对象

    Returns:
        list: 规则质控问题列表
    """
    try:
        # TODO: 这里是规则质控的预留接口
        # 当规则质控模块完成后，在这里调用相应的规则质控函数
        return []

    except Exception as e:
        print(f"规则质控执行异常: {str(e)}")
        return [{
            "score": 0,
            "message": f"规则质控执行异常: {str(e)}",
            "suggestions": "请检查规则质控模块配置"
        }]


def _process_discharge_summary_quality_control(data: Dict[str, Any]) -> tuple[Dict[str, Any], Dict[str, Any]]:
    """
    处理出院小结质控

    调用出院小结总质控函数，并将结果转换为主质控函数的标准返回格式。

    Args:
        data: 出院小结数据对象

    Returns:
        tuple: (处理后的数据对象, 质控结果信息对象)
    """
    try:
        print("执行出院小结质控处理")

        # 调用出院小结总质控函数
        quality_result = discharge_summary_total_quality_control(data)

        # 提取处理后的数据和质控状态
        processed_data = quality_result["data"]
        status = quality_result["status"]

        # 构建质控结果信息对象
        quality_info = {
            "document_type": "DischargeSummary",
            "status":{
                "total_deduction": status["total_deduction"],
                "final_score": status["final_score"],
                "base_score": 120,
                "pass_threshold": 60,
                "passed": status["passed"],
                "quality_status": "通过" if status["passed"] else "不通过",
            },
            "data": processed_data,
            "summary": {
                "total_issues": _count_total_issues(processed_data),
                "recommendations": _get_quality_recommendations(status)
            }
        }

        print(f"出院小结质控处理完成，最终得分: {status['final_score']}/120，结果: {quality_info['quality_status']}")

        return processed_data, quality_info

    except Exception as e:
        print(f"出院小结质控处理异常: {str(e)}")

        # 异常情况下的返回
        error_info = {
            "document_type": "DischargeSummary",
            "error": f"质控处理异常: {str(e)}",
            "total_deduction": 0,
            "final_score": 120,
            "passed": True,  # 异常情况下默认通过
            "quality_status": "异常-默认通过"
        }

        return data, error_info


def quality_control(type: str, data: Dict[str, Any]) -> tuple[Dict[str, Any], Dict[str, Any]]:
    """
    质控主控制函数

    根据文档类型调用相应的质控处理函数，支持多种文书类型的质控检查。
    当前支持的文书类型：
    - "DischargeSummary": 出院小结质控（包括规则质控和内涵质控）

    Args:
        type: 文书类型，支持的值：
            - "DischargeSummary": 出院小结
        data: 待处理的数据对象，具体格式取决于文书类型

    Returns:
        tuple: (处理后的数据对象, 质控结果信息对象)
            - 处理后的数据对象: 包含原始数据和质控结果的完整数据
            - 质控结果信息对象: 包含质控状态、得分、问题统计等信息
    """
    print(f"开始执行质控处理，文书类型: {type}")

    # 验证输入参数
    if not isinstance(data, dict):
        print("错误：输入数据必须是字典格式")
        return data, {"error": "输入数据格式错误，必须是字典格式"}

    # 根据文书类型调用相应的处理函数
    if type == "DischargeSummary":
        return _process_discharge_summary_quality_control(data)
    else:
        print(f"错误：不支持的文书类型 '{type}'")
        supported_types = ["DischargeSummary", "regulatory", "connotation", "both"]
        return data, {
            "error": f"不支持的文书类型: {type}",
            "supported_types": supported_types,
            "message": f"请使用以下支持的文书类型之一: {', '.join(supported_types)}"
        }


def _count_total_issues(processed_data: dict) -> int:
    """
    统计处理后数据中的质控问题总数

    Args:
        processed_data: 处理后的数据对象

    Returns:
        int: 质控问题总数
    """
    total_issues = 0

    try:
        # 递归遍历数据结构，查找质控问题列表
        def count_issues_recursive(obj):
            nonlocal total_issues

            if isinstance(obj, list):
                # 检查是否是质控问题列表
                if obj and isinstance(obj[0], dict) and all(key in obj[0] for key in ["score", "message", "suggestions"]):
                    total_issues += len(obj)
                else:
                    for item in obj:
                        count_issues_recursive(item)
            elif isinstance(obj, dict):
                for value in obj.values():
                    count_issues_recursive(value)

        count_issues_recursive(processed_data)

    except Exception as e:
        print(f"统计质控问题数量时发生异常: {str(e)}")

    return total_issues


def _get_quality_recommendations(status: dict) -> List[str]:
    """
    根据质控状态生成改进建议

    Args:
        status: 质控状态信息

    Returns:
        list: 改进建议列表
    """
    recommendations = []

    try:
        final_score = status.get("final_score", 120)
        total_deduction = status.get("total_deduction", 0)
        passed = status.get("passed", True)

        if not passed:
            recommendations.append("质控未通过，需要立即整改所有发现的问题")
            recommendations.append("建议重点关注扣分较高的问题，优先解决")

        if total_deduction > 50:
            recommendations.append("扣分较多，建议全面检查出院小结的完整性和规范性")
        elif total_deduction > 20:
            recommendations.append("存在一些质控问题，建议及时改进")
        elif total_deduction > 0:
            recommendations.append("发现少量问题，建议完善相关内容")
        else:
            recommendations.append("质控检查通过，文档质量良好")

        if final_score < 80:
            recommendations.append("建议加强医务人员的病历书写培训")

        # 通用建议
        recommendations.append("建议定期进行质控检查，持续改进文档质量")

    except Exception as e:
        print(f"生成质控建议时发生异常: {str(e)}")
        recommendations.append("请联系系统管理员检查质控配置")

    return recommendations